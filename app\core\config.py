"""
Configuration module for Cattlytics AI.

This module defines all configuration classes and settings required for
the application including main settings, knowledge base bot configuration,
and all component-specific configurations.
"""

import os
from enum import Enum
from typing import Any, List, Optional

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class UIFeatureType(str, Enum):
    """Enumeration of UI feature types for software documentation."""
    VIEW_MANAGEMENT = "view_management"
    SEARCH_FILTER = "search_filter"
    DATA_ENTRY = "data_entry"
    ANIMAL_DETAILS = "animal_details"
    NAVIGATION = "navigation"
    GENERAL = "general"


class ParsingConfig(BaseModel):
    """Configuration for document parsing using Docling."""

    # Docling specific settings
    enable_ocr: bool = Field(default=True, description="Enable OCR for image-based content")
    extract_tables: bool = Field(default=True, description="Extract tables from documents")
    extract_images: bool = Field(default=True, description="Extract images from documents")

    # File type support
    supported_formats: List[str] = Field(
        default=["pdf", "docx", "pptx", "html", "md", "txt"],
        description="Supported document formats"
    )

    # Processing settings
    max_file_size_mb: int = Field(default=100, description="Maximum file size in MB")
    timeout_seconds: int = Field(default=300, description="Parsing timeout in seconds")


class ChunkingConfig(BaseModel):
    """Configuration for document chunking and filtering."""

    # Chunking strategy
    chunk_size: int = Field(default=1000, description="Target chunk size in characters")
    chunk_overlap: int = Field(default=200, description="Overlap between chunks in characters")

    # UI feature-based filtering
    enable_ui_feature_filtering: bool = Field(default=True, description="Enable UI feature-based filtering")

    # Chunk quality settings
    min_chunk_length: int = Field(default=100, description="Minimum chunk length in characters")
    max_chunk_length: int = Field(default=2000, description="Maximum chunk length in characters")


class VectorizationConfig(BaseSettings):
    """Configuration for text vectorization using OpenAI models."""

    # OpenAI settings
    OPENAI_API_KEY: str = Field(description="OpenAI API key")
    EMBEDDING_MODEL: str = Field(default="text-embedding-3-small", description="OpenAI embedding model")
    embedding_dimension: int = Field(default=1536, description="Embedding vector dimension")

    # Processing settings
    batch_size: int = Field(default=100, description="Batch size for vectorization")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    retry_delay: float = Field(default=1.0, description="Delay between retries in seconds")

    # Rate limiting
    requests_per_minute: int = Field(default=3000, description="API requests per minute limit")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    @property
    def api_key(self) -> str:
        """Alias for OPENAI_API_KEY for backward compatibility."""
        return self.OPENAI_API_KEY

    @property
    def model_name(self) -> str:
        """Alias for EMBEDDING_MODEL for backward compatibility."""
        return self.EMBEDDING_MODEL


class VectorStoreConfig(BaseSettings):
    """Configuration for Qdrant vector database."""

    # Connection settings
    QDRANT_HOST: str = Field(default="localhost", description="Qdrant host")
    QDRANT_PORT: int = Field(default=6333, description="Qdrant port")
    QDRANT_API_KEY: Optional[str] = Field(default=None, description="Qdrant API key for cloud")

    # Collection settings
    QDRANT_COLLECTION_NAME: str = Field(default="cattle_farming_kb", description="Qdrant collection name")
    vector_size: int = Field(default=1536, description="Vector dimension size")
    distance_metric: str = Field(default="Cosine", description="Distance metric for similarity")

    # Performance settings
    shard_number: int = Field(default=1, description="Number of shards")
    replication_factor: int = Field(default=1, description="Replication factor")

    # Indexing settings
    m: int = Field(default=16, description="HNSW parameter m")
    ef_construct: int = Field(default=100, description="HNSW parameter ef_construct")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    @property
    def host(self) -> str:
        """Alias for QDRANT_HOST for backward compatibility."""
        return self.QDRANT_HOST

    @property
    def port(self) -> int:
        """Alias for QDRANT_PORT for backward compatibility."""
        return self.QDRANT_PORT

    @property
    def api_key(self) -> Optional[str]:
        """Alias for QDRANT_API_KEY for backward compatibility."""
        return self.QDRANT_API_KEY

    @property
    def collection_name(self) -> str:
        """Alias for QDRANT_COLLECTION_NAME for backward compatibility."""
        return self.QDRANT_COLLECTION_NAME


class QueryProcessingConfig(BaseSettings):
    """Configuration for query processing and enhancement."""

    # Query enhancement
    enable_query_expansion: bool = Field(default=True, description="Enable query expansion")
    enable_query_rephrasing: bool = Field(default=True, description="Enable query rephrasing")

    # OpenAI settings for query processing
    OPENAI_API_KEY: str = Field(description="OpenAI API key")
    model_name: str = Field(default="gpt-4o-mini", description="Model for query processing")

    # Processing settings
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    timeout_seconds: int = Field(default=30, description="Query processing timeout")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    @property
    def api_key(self) -> str:
        """Alias for OPENAI_API_KEY for backward compatibility."""
        return self.OPENAI_API_KEY
