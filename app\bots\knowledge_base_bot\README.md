# Knowledge Base Bot

A comprehensive knowledge base bot for cattle farming document ingestion and intelligent retrieval. This bot uses advanced document processing, vectorization, and retrieval techniques to provide accurate answers to cattle farming questions.

## Features

### Document Ingestion
- **Multi-format Support**: PDF, DOCX, PPTX, HTML, MD, TXT
- **Advanced Parsing**: Uses Docling for comprehensive document processing with OCR and table extraction
- **Intelligent Chunking**: UI feature-based filtering for cattle farming content
- **Vectorization**: OpenAI embeddings (text-embedding-3-small) for semantic search
- **Vector Storage**: Qdrant vector database with filtering capabilities

### Query Processing & Retrieval
- **Query Enhancement**: Automatic query rephrasing and expansion
- **UI Feature Classification**: Intelligent filtering based on UI features
- **Semantic Search**: Vector similarity search with feature-based filtering
- **Result Reranking**: Multi-factor relevance scoring
- **Context-Aware**: Maintains conversation history (up to 10 messages)

### Response Generation
- **Contextual Responses**: Uses retrieved chunks and conversation history
- **Source Attribution**: Provides source information for transparency
- **Confidence Scoring**: Estimates response confidence
- **Professional Tone**: Tailored for farmers and livestock managers

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Document      │    │    Ingestion     │    │   Vector Store  │
│   Input         │───▶│    Pipeline      │───▶│   (Qdrant)      │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │   Components:    │
                    │ • DocumentParser │
                    │ • DocumentChunker│
                    │ • Vectorizer     │
                    │ • VectorStore    │
                    └──────────────────┘

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   User Query    │    │   Retrieval      │    │   Response      │
│                 │───▶│   Pipeline       │───▶│   Generation    │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │   Components:    │
                    │ • QueryProcessor │
                    │ • Retriever      │
                    │ • ResponseGen    │
                    └──────────────────┘
```

## Installation

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up Environment Variables**:
   Copy `.env.example` to `.env` and configure:
   ```bash
   cp .env.example .env
   ```

3. **Configure Qdrant**:
   - Local: Install and run Qdrant locally
   - Cloud: Use Qdrant Cloud service

## Configuration

### Required Environment Variables

```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Qdrant Configuration
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=cattle_farming_kb

# Model Configuration
EMBEDDING_MODEL=text-embedding-3-small
RESPONSE_MODEL=gpt-4o-mini
```

### UI Features

The bot classifies content based on UI features for better organization and filtering.

## Usage

### Basic Usage

```python
import asyncio
from app.bots.knowledge_base_bot import KnowledgeBaseBot
from app.core.config import KnowledgeBaseConfig

async def main():
    # Load configuration
    config = KnowledgeBaseConfig.from_env()

    # Initialize bot
    bot = KnowledgeBaseBot(config)

    # Ingest documents
    success = await bot.ingest_document("cattle_guide.pdf")

    # Query the knowledge base
    response = await bot.query("How do I care for newborn calves?")
    print(response["response"])

    # Clean up
    await bot.close()

asyncio.run(main())
```

### Advanced Usage

```python
# Batch document ingestion
document_paths = ["doc1.pdf", "doc2.docx", "doc3.pdf"]
metadata_list = [
    {"topic": "health", "author": "Vet Services"},
    {"topic": "breeding", "author": "Extension Office"},
    {"topic": "nutrition", "author": "Feed Specialist"}
]

results = await bot.ingest_documents(document_paths, metadata_list)

# Query with conversation history
conversation_history = [
    {"role": "user", "content": "I'm new to cattle farming"},
    {"role": "assistant", "content": "I'd be happy to help!"}
]

response = await bot.query(
    "What should I know about calf nutrition?",
    conversation_history=conversation_history
)
```

## API Reference

### KnowledgeBaseBot

#### Methods

- `ingest_document(document_path, metadata=None)`: Ingest single document
- `ingest_documents(document_paths, metadata_list=None)`: Ingest multiple documents
- `query(user_query, conversation_history=None)`: Process query and generate response
- `delete_document(file_path)`: Remove document from knowledge base
- `health_check()`: Check system health
- `get_collection_info()`: Get vector store statistics
- `close()`: Clean up resources

### Response Format

```python
{
    "response": "Generated response text",
    "sources": [
        {
            "source_id": 1,
            "file_name": "cattle_guide.pdf",
            "relevance_score": 0.85,
            "ui_features": ["animal_details"]
        }
    ],
    "confidence": 0.8,
    "tokens_used": 150,
    "processing_time": 2.3,
    "success": True,
    "query_info": {
        "enhanced_query": "Enhanced query text",
        "ui_features": ["animal_details"],
        "keywords": ["nutrition", "feeding"]
    }
}
```

## File Structure

```
app/bots/knowledge_base_bot/
├── __init__.py
├── knowledge_base.py          # Main orchestrator class
├── utils.py                   # Utility functions
├── example_usage.py           # Usage examples
├── README.md                  # This file
├── ingestion/
│   ├── __init__.py
│   ├── document_parser.py     # Document parsing with Docling
│   ├── document_chunker.py    # Intelligent chunking
│   ├── vectorizer.py          # OpenAI embeddings
│   └── vector_store.py        # Qdrant integration
└── retrieval/
    ├── __init__.py
    ├── query_processor.py     # Query enhancement
    ├── retriever.py           # Semantic search
    └── response_generator.py  # Response generation

app/core/
└── config.py                  # Centralized configuration management
```

## Dependencies

- **docling**: Document parsing and OCR
- **openai**: Embeddings and chat completions
- **qdrant-client**: Vector database client
- **loguru**: Structured logging
- **pydantic**: Configuration validation
- **semchunk**: Semantic text chunking
- **numpy**: Numerical operations

## Performance Considerations

- **Batch Processing**: Process multiple documents concurrently
- **Rate Limiting**: Respects OpenAI API rate limits
- **Chunking Strategy**: Optimized for cattle farming content
- **Vector Storage**: Efficient similarity search with Qdrant
- **Memory Management**: Proper resource cleanup

## Troubleshooting

### Common Issues

1. **OpenAI API Errors**: Check API key and rate limits
2. **Qdrant Connection**: Verify host, port, and collection settings
3. **Document Parsing**: Ensure supported file formats
4. **Memory Issues**: Reduce batch sizes for large documents

### Health Check

```python
health_status = await bot.health_check()
print(health_status)
```

## Contributing

1. Follow the existing code structure and patterns
2. Add comprehensive docstrings and type hints
3. Include error handling and logging
4. Write tests for new functionality
5. Update documentation as needed

## License

This project is part of the Cattlytics AI system.
