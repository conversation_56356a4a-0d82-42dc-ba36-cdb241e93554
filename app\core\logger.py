"""
Custom Logger Implementation for Cattlytics AI

This module provides a custom logger that can be used throughout the application
instead of directly importing loguru. It provides error, debug, success, info,
warning, and other logging methods with consistent formatting and behavior.
"""

import sys
import logging
from datetime import datetime
from typing import Any, Optional, Union, Dict
from pathlib import Path
from enum import Enum


class LogLevel(Enum):
    """Log level enumeration."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    SUCCESS = "SUCCESS"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class ColorCodes:
    """ANSI color codes for console output."""
    RESET = "\033[0m"
    BOLD = "\033[1m"

    # Colors
    BLACK = "\033[30m"
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"

    # Bright colors
    BRIGHT_BLACK = "\033[90m"
    BRIGHT_RED = "\033[91m"
    BRIGHT_GREEN = "\033[92m"
    BRIGHT_YELLOW = "\033[93m"
    BRIGHT_BLUE = "\033[94m"
    BRIGHT_MAGENTA = "\033[95m"
    BRIGHT_CYAN = "\033[96m"
    BRIGHT_WHITE = "\033[97m"


class CustomLogger:
    """
    Custom logger implementation that provides loguru-like interface
    with additional functionality for error, debug, success logging.
    """

    def __init__(self, name: str = "cattlytics"):
        """
        Initialize the custom logger.

        Args:
            name: Logger name
        """
        self.name = name
        self.handlers: Dict[str, Any] = {}
        self._setup_default_handler()

    def _setup_default_handler(self):
        """Set up default console handler."""
        self.add_console_handler()

    def _get_timestamp(self) -> str:
        """Get formatted timestamp."""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _get_caller_info(self) -> Dict[str, str]:
        """Get caller information (file, function, line)."""
        import inspect

        # Go up the stack to find the actual caller (skip logger methods)
        frame = inspect.currentframe()
        try:
            # Skip current frame and logger method frames
            for _ in range(3):
                frame = frame.f_back
                if frame is None:
                    break

            if frame:
                filename = Path(frame.f_code.co_filename).name
                function = frame.f_code.co_name
                line = frame.f_lineno
                return {
                    "file": filename,
                    "function": function,
                    "line": str(line)
                }
        finally:
            del frame

        return {"file": "unknown", "function": "unknown", "line": "0"}

    def _format_message(self, level: LogLevel, message: str, colorize: bool = True) -> str:
        """
        Format log message with timestamp, level, caller info, and message.

        Args:
            level: Log level
            message: Log message
            colorize: Whether to add color codes

        Returns:
            Formatted message string
        """
        timestamp = self._get_timestamp()
        caller = self._get_caller_info()

        if colorize:
            # Color mapping for different log levels
            level_colors = {
                LogLevel.DEBUG: ColorCodes.BRIGHT_BLACK,
                LogLevel.INFO: ColorCodes.BLUE,
                LogLevel.SUCCESS: ColorCodes.GREEN,
                LogLevel.WARNING: ColorCodes.YELLOW,
                LogLevel.ERROR: ColorCodes.RED,
                LogLevel.CRITICAL: ColorCodes.BRIGHT_RED + ColorCodes.BOLD,
            }

            color = level_colors.get(level, ColorCodes.WHITE)
            reset = ColorCodes.RESET

            formatted = (
                f"{ColorCodes.GREEN}{timestamp}{reset} | "
                f"{color}{level.value: <8}{reset} | "
                f"{ColorCodes.CYAN}{caller['file']}{reset}:"
                f"{ColorCodes.CYAN}{caller['function']}{reset}:"
                f"{ColorCodes.CYAN}{caller['line']}{reset} - "
                f"{color}{message}{reset}"
            )
        else:
            formatted = (
                f"{timestamp} | {level.value: <8} | "
                f"{caller['file']}:{caller['function']}:{caller['line']} - {message}"
            )

        return formatted

    def add_console_handler(self, level: str = "INFO", colorize: bool = True):
        """
        Add console handler for logging to stdout.

        Args:
            level: Minimum log level
            colorize: Whether to colorize output
        """
        handler_id = "console"
        self.handlers[handler_id] = {
            "type": "console",
            "level": level,
            "colorize": colorize,
            "sink": sys.stdout
        }

    def add_file_handler(
        self,
        file_path: Union[str, Path],
        level: str = "INFO",
        rotation: Optional[str] = "10 MB",
        retention: Optional[str] = "7 days"
    ):
        """
        Add file handler for logging to file.

        Args:
            file_path: Path to log file
            level: Minimum log level
            rotation: File rotation size (e.g., "10 MB")
            retention: Log retention period (e.g., "7 days")
        """
        handler_id = f"file_{file_path}"
        self.handlers[handler_id] = {
            "type": "file",
            "level": level,
            "colorize": False,
            "sink": file_path,
            "rotation": rotation,
            "retention": retention
        }

    def remove_handler(self, handler_id: str):
        """Remove a specific handler."""
        if handler_id in self.handlers:
            del self.handlers[handler_id]

    def remove_all_handlers(self):
        """Remove all handlers."""
        self.handlers.clear()

    def _should_log(self, level: LogLevel, handler_level: str) -> bool:
        """Check if message should be logged based on level."""
        level_hierarchy = {
            "DEBUG": 0,
            "INFO": 1,
            "SUCCESS": 1,  # Same as INFO
            "WARNING": 2,
            "ERROR": 3,
            "CRITICAL": 4
        }

        return level_hierarchy.get(level.value, 1) >= level_hierarchy.get(handler_level, 1)

    def _log(self, level: LogLevel, message: str, **kwargs):
        """
        Internal logging method.

        Args:
            level: Log level
            message: Log message
            **kwargs: Additional arguments (for compatibility)
        """
        for handler_id, handler in self.handlers.items():
            if self._should_log(level, handler["level"]):
                formatted_message = self._format_message(
                    level,
                    message,
                    colorize=handler.get("colorize", False)
                )

                if handler["type"] == "console":
                    print(formatted_message, file=handler["sink"])
                elif handler["type"] == "file":
                    # For file logging, we'll use simple append for now
                    # In a production system, you might want to implement proper rotation
                    try:
                        with open(handler["sink"], "a", encoding="utf-8") as f:
                            f.write(formatted_message + "\n")
                    except Exception as e:
                        # Fallback to console if file logging fails
                        print(f"Failed to write to log file: {e}", file=sys.stderr)
                        print(formatted_message, file=sys.stdout)

    # Public logging methods with loguru-compatible interface
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self._log(LogLevel.DEBUG, message, **kwargs)

    def info(self, message: str, **kwargs):
        """Log info message."""
        self._log(LogLevel.INFO, message, **kwargs)

    def success(self, message: str, **kwargs):
        """Log success message."""
        self._log(LogLevel.SUCCESS, message, **kwargs)

    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self._log(LogLevel.WARNING, message, **kwargs)

    def error(self, message: str, **kwargs):
        """Log error message."""
        self._log(LogLevel.ERROR, message, **kwargs)

    def critical(self, message: str, **kwargs):
        """Log critical message."""
        self._log(LogLevel.CRITICAL, message, **kwargs)

    # Aliases for compatibility
    warn = warning
    exception = error


# Create a global logger instance
logger = CustomLogger("cattlytics")


# Utility functions for logger configuration
def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[Union[str, Path]] = None,
    colorize: bool = True
):
    """
    Set up logging configuration.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional log file path
        colorize: Whether to colorize console output
    """
    # Remove all existing handlers
    logger.remove_all_handlers()

    # Add console handler
    logger.add_console_handler(level=log_level, colorize=colorize)

    # Add file handler if specified
    if log_file:
        logger.add_file_handler(
            file_path=log_file,
            level=log_level,
            rotation="10 MB",
            retention="7 days"
        )

    logger.info(f"Custom logging configured with level: {log_level}")


def get_logger(name: str = "cattlytics") -> CustomLogger:
    """
    Get a logger instance.

    Args:
        name: Logger name

    Returns:
        CustomLogger instance
    """
    return CustomLogger(name)