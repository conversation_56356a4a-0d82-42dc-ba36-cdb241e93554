"""
Utility functions for the Knowledge Base Bot.

This module contains helper functions for file handling, text processing,
validation, and other common operations used throughout the bot.
"""

import os
import hashlib
import mimetypes
from typing import List, Dict, Any, Optional, Set, Union
from pathlib import Path
import asyncio
import time
from datetime import datetime
from core.logger import logger




def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """
    Set up logging configuration for the knowledge base bot.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Optional log file path
    """
    # Remove default logger
    logger.remove()
    
    # Add console logger
    logger.add(
        sink=lambda msg: print(msg, end=""),
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # Add file logger if specified
    if log_file:
        logger.add(
            sink=log_file,
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB",
            retention="7 days",
            compression="zip"
        )
    
    logger.info(f"Logging configured with level: {log_level}")


def get_file_hash(file_path: Union[str, Path]) -> str:
    """
    Calculate SHA-256 hash of a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Hexadecimal hash string
    """
    hash_sha256 = hashlib.sha256()
    
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    except Exception as e:
        logger.error(f"Error calculating hash for {file_path}: {str(e)}")
        return ""


def get_file_info(file_path: Union[str, Path]) -> Dict[str, Any]:
    """
    Get comprehensive file information.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Dict with file information
    """
    try:
        path = Path(file_path)
        stat = path.stat()
        
        return {
            "file_name": path.name,
            "file_path": str(path.absolute()),
            "file_size": stat.st_size,
            "file_extension": path.suffix.lower(),
            "mime_type": mimetypes.guess_type(str(path))[0],
            "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
            "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
            "file_hash": get_file_hash(path)
        }
    except Exception as e:
        logger.error(f"Error getting file info for {file_path}: {str(e)}")
        return {}


def is_supported_file_type(file_path: Union[str, Path]) -> bool:
    """
    Check if file type is supported for processing.
    
    Args:
        file_path: Path to the file
        
    Returns:
        True if file type is supported
    """
    supported_extensions = {
        '.pdf', '.docx', '.pptx', '.html', '.htm', '.md', '.txt'
    }
    
    extension = Path(file_path).suffix.lower()
    return extension in supported_extensions


def validate_file_for_processing(file_path: Union[str, Path]) -> Dict[str, Any]:
    """
    Validate file for document processing.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Dict with validation results
    """
    path = Path(file_path)
    
    validation_result = {
        "valid": False,
        "errors": [],
        "warnings": [],
        "file_info": {}
    }
    
    # Check if file exists
    if not path.exists():
        validation_result["errors"].append("File does not exist")
        return validation_result
    
    # Check if it's a file (not directory)
    if not path.is_file():
        validation_result["errors"].append("Path is not a file")
        return validation_result
    
    # Get file info
    file_info = get_file_info(path)
    validation_result["file_info"] = file_info
    
    # Check file size
    max_size = 100 * 1024 * 1024  # 100MB
    if file_info.get("file_size", 0) > max_size:
        validation_result["errors"].append(f"File too large: {file_info['file_size']} bytes (max: {max_size})")
    
    # Check if file is empty
    if file_info.get("file_size", 0) == 0:
        validation_result["errors"].append("File is empty")
    
    # Check file type
    if not is_supported_file_type(path):
        validation_result["errors"].append(f"Unsupported file type: {file_info.get('file_extension', 'unknown')}")
    
    # Add warnings for potentially problematic files
    if file_info.get("file_size", 0) > 10 * 1024 * 1024:  # 10MB
        validation_result["warnings"].append("Large file may take longer to process")
    
    # Set valid flag
    validation_result["valid"] = len(validation_result["errors"]) == 0
    
    return validation_result


def clean_text(text: str) -> str:
    """
    Clean and normalize text content.
    
    Args:
        text: Raw text to clean
        
    Returns:
        Cleaned text
    """
    if not text:
        return ""
    
    # Remove excessive whitespace
    text = " ".join(text.split())
    
    # Remove control characters except newlines and tabs
    cleaned_chars = []
    for char in text:
        if char.isprintable() or char in ['\n', '\t']:
            cleaned_chars.append(char)
    
    text = "".join(cleaned_chars)
    
    # Normalize line endings
    text = text.replace('\r\n', '\n').replace('\r', '\n')
    
    # Remove excessive newlines
    while '\n\n\n' in text:
        text = text.replace('\n\n\n', '\n\n')
    
    return text.strip()


def extract_keywords(text: str, min_length: int = 3, max_keywords: int = 20) -> List[str]:
    """
    Extract keywords from text using simple frequency analysis.
    
    Args:
        text: Text to extract keywords from
        min_length: Minimum keyword length
        max_keywords: Maximum number of keywords to return
        
    Returns:
        List of keywords
    """
    if not text:
        return []
    
    # Common stop words to exclude
    stop_words = {
        'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
        'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
        'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
        'those', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have',
        'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
        'may', 'might', 'must', 'can', 'shall', 'a', 'an', 'as', 'if', 'when',
        'where', 'why', 'how', 'what', 'which', 'who', 'whom', 'whose', 'all',
        'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such',
        'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very'
    }
    
    # Extract words
    words = text.lower().split()
    
    # Filter and count words
    word_counts = {}
    for word in words:
        # Remove punctuation
        word = ''.join(char for char in word if char.isalnum())
        
        # Skip if too short or is stop word
        if len(word) < min_length or word in stop_words:
            continue
        
        word_counts[word] = word_counts.get(word, 0) + 1
    
    # Sort by frequency and return top keywords
    sorted_words = sorted(word_counts.items(), key=lambda x: x[1], reverse=True)
    keywords = [word for word, count in sorted_words[:max_keywords]]
    
    return keywords


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human-readable format.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def format_duration(seconds: float) -> str:
    """
    Format duration in human-readable format.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 1:
        return f"{seconds*1000:.0f}ms"
    elif seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes}m {secs}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours}h {minutes}m"


async def batch_process(
    items: List[Any],
    process_func,
    batch_size: int = 10,
    delay_between_batches: float = 0.1
) -> List[Any]:
    """
    Process items in batches with optional delay.
    
    Args:
        items: List of items to process
        process_func: Async function to process each item
        batch_size: Number of items per batch
        delay_between_batches: Delay between batches in seconds
        
    Returns:
        List of processed results
    """
    results = []
    
    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        
        # Process batch concurrently
        batch_tasks = [process_func(item) for item in batch]
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
        
        # Filter out exceptions and add to results
        for result in batch_results:
            if not isinstance(result, Exception):
                results.append(result)
            else:
                logger.error(f"Error in batch processing: {str(result)}")
        
        # Delay between batches if specified
        if delay_between_batches > 0 and i + batch_size < len(items):
            await asyncio.sleep(delay_between_batches)
    
    return results


class Timer:
    """Simple timer context manager for measuring execution time."""
    
    def __init__(self, description: str = "Operation"):
        self.description = description
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        duration = self.end_time - self.start_time
        logger.info(f"{self.description} completed in {format_duration(duration)}")
    
    @property
    def elapsed(self) -> float:
        """Get elapsed time in seconds."""
        if self.start_time is None:
            return 0.0
        
        end_time = self.end_time or time.time()
        return end_time - self.start_time


def create_directory_if_not_exists(directory_path: Union[str, Path]) -> bool:
    """
    Create directory if it doesn't exist.
    
    Args:
        directory_path: Path to directory
        
    Returns:
        True if directory exists or was created successfully
    """
    try:
        Path(directory_path).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Error creating directory {directory_path}: {str(e)}")
        return False



