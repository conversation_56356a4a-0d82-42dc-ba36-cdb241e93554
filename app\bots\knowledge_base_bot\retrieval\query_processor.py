"""
Query Processor for enhancing and processing user queries.

This module handles query enhancement, rephrasing, and expansion
to improve retrieval accuracy and relevance.
"""

from typing import Dict, Any, List, Optional
import json
from dataclasses import dataclass
from core.logger import logger

from openai import AsyncOpenAI

from core.config import QueryProcessingConfig


@dataclass
class ProcessedQuery:
    """
    Container for processed query information.
    """
    original_query: str
    enhanced_query: str
    rephrased_queries: List[str]
    keywords: List[str]
    intent: str
    confidence: float

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "original_query": self.original_query,
            "enhanced_query": self.enhanced_query,
            "rephrased_queries": self.rephrased_queries,
            "keywords": self.keywords,
            "intent": self.intent,
            "confidence": self.confidence
        }


class QueryProcessor:
    """
    Query processor for enhancing user queries.

    Handles query expansion, rephrasing, and keyword extraction
    to improve retrieval performance.
    """
    
    def __init__(self, config: QueryProcessingConfig):
        """
        Initialize the query processor.
        
        Args:
            config: Query processing configuration
        """
        self.config = config
        self.client = AsyncOpenAI(api_key=config.api_key)
        logger.info("Query processor initialized")
    
    async def process_query(
        self,
        query: str,
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> ProcessedQuery:
        """
        Process and enhance a user query.
        
        Args:
            query: Original user query
            conversation_history: Optional conversation context
            
        Returns:
            ProcessedQuery: Enhanced query information
        """
        try:
            logger.info(f"Processing query: {query}")
            
            # Initialize processed query
            processed_query = ProcessedQuery(
                original_query=query,
                enhanced_query=query,
                rephrased_queries=[],
                keywords=[],
                intent="general",
                confidence=0.5
            )

            # Enhance query if enabled
            if self.config.enable_query_expansion or self.config.enable_query_rephrasing:
                enhanced_data = await self._enhance_query(query, conversation_history)
                if enhanced_data:
                    processed_query.enhanced_query = enhanced_data.get("enhanced_query", query)
                    processed_query.rephrased_queries = enhanced_data.get("rephrased_queries", [])
                    processed_query.keywords = enhanced_data.get("keywords", [])
                    processed_query.intent = enhanced_data.get("intent", "general")
                    processed_query.confidence = enhanced_data.get("confidence", 0.5)

            logger.info(f"Query processed successfully: {len(processed_query.rephrased_queries)} variations")
            return processed_query
            
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            # Return basic processed query on error
            return ProcessedQuery(
                original_query=query,
                enhanced_query=query,
                rephrased_queries=[],
                keywords=[],
                intent="general",
                confidence=0.1
            )
    
    async def _enhance_query(
        self,
        query: str,
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Enhance query using LLM for expansion and rephrasing.
        
        Args:
            query: Original query
            conversation_history: Conversation context
            
        Returns:
            Dict with enhanced query data or None if failed
        """
        try:
            # Build context from conversation history
            context = ""
            if conversation_history:
                recent_history = conversation_history[-5:]  # Last 5 exchanges
                context_parts = []
                for exchange in recent_history:
                    if exchange.get("role") == "user":
                        context_parts.append(f"User: {exchange.get('content', '')}")
                    elif exchange.get("role") == "assistant":
                        context_parts.append(f"Assistant: {exchange.get('content', '')}")
                context = "\n".join(context_parts)
            
            # Create enhancement prompt
            system_prompt = """You are an expert at understanding cattle farming and livestock management queries. Your task is to enhance user queries to improve information retrieval.

Given a user query about cattle farming, provide:
1. An enhanced/expanded version of the query with relevant synonyms and context
2. 2-3 rephrased versions of the query
3. Key keywords for search
4. The intent/topic of the query
5. Confidence score (0.0-1.0) for your understanding

Focus on cattle farming terminology, breeding, health, management, and agricultural practices.

Respond in JSON format:
{
    "enhanced_query": "expanded query with synonyms and context",
    "rephrased_queries": ["variation 1", "variation 2", "variation 3"],
    "keywords": ["keyword1", "keyword2", "keyword3"],
    "intent": "breeding|health|management|feeding|general",
    "confidence": 0.8
}"""
            
            user_prompt = f"""Query: "{query}"

Context from conversation:
{context if context else "No previous context"}

Please enhance this query for better cattle farming information retrieval."""
            
            # Call OpenAI API
            response = await self.client.chat.completions.create(
                model=self.config.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.3,
                max_tokens=500,
                timeout=self.config.timeout_seconds
            )
            
            # Parse response
            response_text = response.choices[0].message.content.strip()
            
            # Try to parse JSON response
            try:
                enhanced_data = json.loads(response_text)
                return enhanced_data
            except json.JSONDecodeError:
                logger.warning("Failed to parse JSON response from query enhancement")
                return None
                
        except Exception as e:
            logger.error(f"Error enhancing query: {str(e)}")
            return None
    

    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the query processor.
        
        Returns:
            Dict containing health status
        """
        try:
            # Test with a simple query
            test_query = "How do I care for dairy cows?"
            processed = await self.process_query(test_query)
            
            return {
                "healthy": True,
                "model": self.config.model_name,
                "query_expansion_enabled": self.config.enable_query_expansion,
                "query_rephrasing_enabled": self.config.enable_query_rephrasing,
                "test_query_processed": processed.enhanced_query != test_query,
                "keywords_extracted": len(processed.keywords) > 0
            }
            
        except Exception as e:
            logger.error(f"Query processor health check failed: {str(e)}")
            return {
                "healthy": False,
                "error": str(e)
            }
